import 'package:audioplayers/audioplayers.dart';
import 'package:mp3_player/models/playlist.dart';

class AudioService {
  final AudioPlayer _audioPlayer = AudioPlayer();
  Playlist? _currentPlaylist;
  bool _isPlaying = false;

  bool get isPlaying => _isPlaying;
  Playlist? get currentPlaylist => _currentPlaylist;

  AudioService() {
    // Listen for when a song completes
    _audioPlayer.onPlayerComplete.listen((event) {
      _onSongComplete();
    });
  }

  // Get current playlist
  Playlist? getCurrentPlaylist() => _currentPlaylist;

  // Get current position
  Future<Duration?> getCurrentPosition() async {
    return await _audioPlayer.getCurrentPosition();
  }

  // Get duration
  Future<Duration?> getDuration() async {
    return await _audioPlayer.getDuration();
  }

  // Seek to position
  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  Future<void> playPlaylist(Playlist playlist) async {
    _currentPlaylist = playlist;
    await _playCurrentSong();
  }

  Future<void> _playCurrentSong() async {
    if (_currentPlaylist == null) return;

    final currentSong = _currentPlaylist!.getCurrentSong();
    if (currentSong == null) return;

    await _audioPlayer.play(DeviceFileSource(currentSong));
    _isPlaying = true;
  }

  Future<void> togglePlayPause() async {
    if (_currentPlaylist == null) return;

    if (_isPlaying) {
      await _audioPlayer.pause();
    } else {
      await _audioPlayer.resume();
    }
    _isPlaying = !_isPlaying;
  }

  Future<void> nextSong() async {
    if (_currentPlaylist == null) return;

    _currentPlaylist!.nextSong();
    await _playCurrentSong();
  }

  Future<void> previousSong() async {
    if (_currentPlaylist == null) return;

    _currentPlaylist!.previousSong();
    await _playCurrentSong();
  }

  // Handle song completion - automatically advance to next song
  void _onSongComplete() {
    if (_currentPlaylist != null && _currentPlaylist!.hasNextSong()) {
      // Automatically advance to next song
      nextSong();
    } else {
      // End of playlist reached
      _isPlaying = false;
    }
  }

  Future<void> dispose() async {
    await _audioPlayer.dispose();
  }
}
