import 'dart:async';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:mp3_player/models/playlist.dart';
import 'package:mp3_player/services/audio_service.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class PlayerScreen extends StatefulWidget {
  const PlayerScreen({super.key});

  @override
  State<PlayerScreen> createState() => _PlayerScreenState();
}

class _PlayerScreenState extends State<PlayerScreen> {
  List<Playlist> playlists = [];
  int currentPlaylistIndex = 0;
  int addPlaylistIndex = 0;
  final AudioService _audioService = AudioService();
  TextEditingController searchController = TextEditingController();
  String? _currentBrowsingPath;
  List<FileSystemEntity> _currentBrowseContents = [];
  List<String> _searchResults = [];
  bool _isSearching = false;
  bool _showAllSections = true;
  final Map<String, bool> _expandedFolders = {};
  String? _currentlyPlayingFile;
  Timer? _progressTimer;

  // New data structure for search results with folders
  Map<String, List<String>> _searchFolderResults = {};
  List<String> _searchFolderPaths = [];

  @override
  void initState() {
    super.initState();
    _loadPlaylists();
    _requestPermissions().then((_) async {
      final externalStorage = (await getExternalStorageDirectory())?.path;
      if (externalStorage != null) {
        final String rootExternal = externalStorage.split('/Android').first;
        _navigateToDirectory(rootExternal);
      }
    });
    searchController.addListener(_onSearchChanged);

    // Start timer for updating UI
    _progressTimer = Timer.periodic(Duration(milliseconds: 500), (timer) {
      if (mounted) {
        setState(() {
          // This will trigger UI updates for progress bars and play/pause button
        });
      }
    });
  }

  // Initialize 50 playlists
  void _initializePlaylists() {
    playlists = List.generate(
      50,
      (index) => Playlist(name: 'Playlist ${index + 1}'),
    );
  }

  // Load playlists from SharedPreferences
  Future<void> _loadPlaylists() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final playlistsJson = prefs.getStringList('playlists');

      if (playlistsJson != null && playlistsJson.isNotEmpty) {
        setState(() {
          playlists = playlistsJson.map((json) {
            final Map<String, dynamic> data = jsonDecode(json);
            final playlist = Playlist(
              name: data['name'],
              songs: List<String>.from(data['songs'] ?? []),
              currentIndex: data['currentIndex'] ?? 0,
            );
            return playlist;
          }).toList();

          // Ensure we have exactly 50 playlists
          while (playlists.length < 50) {
            playlists.add(Playlist(name: 'Playlist ${playlists.length + 1}'));
          }
          if (playlists.length > 50) {
            playlists = playlists.take(50).toList();
          }

          currentPlaylistIndex = prefs.getInt('currentPlaylistIndex') ?? 0;
          addPlaylistIndex = prefs.getInt('addPlaylistIndex') ?? 0;

          // Ensure indices are within bounds
          if (currentPlaylistIndex >= playlists.length) {
            currentPlaylistIndex = 0;
          }
          if (addPlaylistIndex >= playlists.length) {
            addPlaylistIndex = 0;
          }

          // Load last playback state
          _loadPlaybackState();
        });
      } else {
        setState(() {
          _initializePlaylists();
        });
      }
    } catch (e) {
      debugPrint('Error loading playlists: $e');
      setState(() {
        _initializePlaylists();
      });
    }
  }

  // Load playback state
  Future<void> _loadPlaybackState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastPlayingPlaylist = prefs.getInt('lastPlayingPlaylist');
      final lastPlayingIndex = prefs.getInt('lastPlayingIndex');
      final lastPosition = prefs.getInt('lastPosition') ?? 0;

      if (lastPlayingPlaylist != null &&
          lastPlayingIndex != null &&
          lastPlayingPlaylist < playlists.length &&
          lastPlayingIndex < playlists[lastPlayingPlaylist].songs.length) {
        // Restore the last playing state
        currentPlaylistIndex = lastPlayingPlaylist;
        playlists[currentPlaylistIndex].currentIndex = lastPlayingIndex;
        _currentlyPlayingFile =
            playlists[currentPlaylistIndex].songs[lastPlayingIndex];

        // Set up audio service with the last playlist
        _audioService.playPlaylist(playlists[currentPlaylistIndex]);

        // Seek to last position if it was saved
        if (lastPosition > 0) {
          _audioService.seek(Duration(milliseconds: lastPosition));
        }
      }
    } catch (e) {
      debugPrint('Error loading playback state: $e');
    }
  }

  // Save playlists to SharedPreferences
  Future<void> _savePlaylists() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final playlistsJson = playlists.map((playlist) {
        return jsonEncode({
          'name': playlist.name,
          'songs': playlist.songs,
          'currentIndex': playlist.currentIndex,
        });
      }).toList();

      await prefs.setStringList('playlists', playlistsJson);
      await prefs.setInt('currentPlaylistIndex', currentPlaylistIndex);
      await prefs.setInt('addPlaylistIndex', addPlaylistIndex);
    } catch (e) {
      debugPrint('Error saving playlists: $e');
    }
  }

  // Save playback state
  Future<void> _savePlaybackState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentPlaylist = _audioService.getCurrentPlaylist();

      if (currentPlaylist != null) {
        await prefs.setInt('lastPlayingPlaylist', currentPlaylistIndex);
        await prefs.setInt('lastPlayingIndex', currentPlaylist.currentIndex);

        // Save current position
        final position = await _audioService.getCurrentPosition();
        await prefs.setInt('lastPosition', position?.inMilliseconds ?? 0);
      }
    } catch (e) {
      debugPrint('Error saving playback state: $e');
    }
  }

  Future<void> _requestPermissions() async {
    final storageStatus = await Permission.storage.request();
    debugPrint('Storage permission status: $storageStatus');

    // For Android 11+ (API 30+), MANAGE_EXTERNAL_STORAGE is needed for full access
    if (Platform.isAndroid) {
      final deviceInfoPlugin = DeviceInfoPlugin();
      final androidInfo = await deviceInfoPlugin.androidInfo;
      if (androidInfo.version.sdkInt >= 30) {
        final manageStatus = await Permission.manageExternalStorage.request();
        debugPrint('Manage external storage permission status: $manageStatus');
        if (manageStatus.isDenied || manageStatus.isPermanentlyDenied) {
          // Prompt user to open app settings
          await openAppSettings();
        }
      }
    }

    // Also request audio permissions for better compatibility
    await Permission.audio.request();

    // Request microphone permission (sometimes needed for audio access)
    await Permission.microphone.request();
  }

  Future<void> _addFileToPlaylist(String filePath) async {
    setState(() {
      playlists[currentPlaylistIndex].addSong(filePath);
    });
    await _savePlaylists(); // Save after adding
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Added ${path.basename(filePath)} to Playlist ${currentPlaylistIndex + 1}',
          ),
        ),
      );
    }
  }

  Future<void> _addFolderToPlaylist(String folderPath) async {
    final dir = Directory(folderPath);
    if (!await dir.exists()) return;

    // Skip restricted directories
    if (_isRestrictedDirectory(folderPath)) {
      debugPrint('Skipping restricted directory: $folderPath');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Cannot access restricted directory')),
        );
      }
      return;
    }

    List<String> songsToAdd = [];
    try {
      await for (var entity in dir.list(recursive: true, followLinks: false)) {
        if (entity is File) {
          final fileName = entity.path.toLowerCase();
          if (fileName.endsWith('.mp3') ||
              fileName.endsWith('.wav') ||
              fileName.endsWith('.flac') ||
              fileName.endsWith('.m4a') ||
              fileName.endsWith('.aac') ||
              fileName.endsWith('.ogg')) {
            songsToAdd.add(entity.path);
          }
        }
      }
    } catch (e) {
      debugPrint('Error scanning folder $folderPath: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error accessing folder: Permission denied'),
          ),
        );
      }
      return;
    }

    // Sort songs by folder name then alphabetically
    songsToAdd.sort((a, b) {
      final folderA = path.dirname(a);
      final folderB = path.dirname(b);
      final folderComparison = folderA.compareTo(folderB);
      if (folderComparison != 0) return folderComparison;
      return path.basename(a).compareTo(path.basename(b));
    });

    setState(() {
      playlists[currentPlaylistIndex].addSongs(songsToAdd);
    });
    await _savePlaylists(); // Save after adding
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Added ${songsToAdd.length} music files from ${path.basename(folderPath)} to Playlist ${currentPlaylistIndex + 1}',
          ),
        ),
      );
    }
  }

  // Remove folder and all its files from playlist
  Future<void> _removeFolderFromPlaylist(String folderPath) async {
    final currentPlaylist = playlists[currentPlaylistIndex];

    // Find all songs from this folder and remove them
    List<String> songsToRemove = [];
    for (String songPath in currentPlaylist.songs) {
      if (path.dirname(songPath) == folderPath) {
        songsToRemove.add(songPath);
      }
    }

    // Remove all songs from this folder
    for (String songPath in songsToRemove) {
      int index = currentPlaylist.songs.indexOf(songPath);
      if (index != -1) {
        currentPlaylist.removeSong(index);
      }
    }

    setState(() {});
    await _savePlaylists();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Removed ${songsToRemove.length} songs from ${path.basename(folderPath)} folder',
          ),
        ),
      );
    }
  }

  // Play folder directly (temporary playlist)
  Future<void> _playFolder(String folderPath) async {
    final dir = Directory(folderPath);
    if (!await dir.exists()) return;

    // Skip restricted directories
    if (_isRestrictedDirectory(folderPath)) {
      debugPrint('Skipping restricted directory: $folderPath');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Cannot access restricted directory')),
        );
      }
      return;
    }

    List<String> songsToPlay = [];
    try {
      await for (var entity in dir.list(recursive: true, followLinks: false)) {
        if (entity is File) {
          final fileName = entity.path.toLowerCase();
          if (fileName.endsWith('.mp3') ||
              fileName.endsWith('.wav') ||
              fileName.endsWith('.flac') ||
              fileName.endsWith('.m4a') ||
              fileName.endsWith('.aac') ||
              fileName.endsWith('.ogg')) {
            songsToPlay.add(entity.path);
          }
        }
      }
    } catch (e) {
      debugPrint('Error scanning folder $folderPath: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error accessing folder: Permission denied'),
          ),
        );
      }
      return;
    }

    // Sort songs by folder name then alphabetically
    songsToPlay.sort((a, b) {
      final folderA = path.dirname(a);
      final folderB = path.dirname(b);
      final folderComparison = folderA.compareTo(folderB);
      if (folderComparison != 0) return folderComparison;
      return path.basename(a).compareTo(path.basename(b));
    });

    if (songsToPlay.isNotEmpty) {
      // Create a temporary playlist for playing
      final tempPlaylist = Playlist(
        name: 'Playing: ${path.basename(folderPath)}',
        songs: songsToPlay,
        currentIndex: 0, // Start from the first song
      );

      // Start new playlist from beginning
      _audioService.playPlaylist(tempPlaylist);
      _currentlyPlayingFile = songsToPlay[0];
      setState(() {});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Playing ${songsToPlay.length} songs from ${path.basename(folderPath)}',
            ),
          ),
        );
      }
    }
  }

  void _playPlaylist() {
    _audioService.playPlaylist(playlists[currentPlaylistIndex]);
    if (playlists[currentPlaylistIndex].songs.isNotEmpty) {
      _currentlyPlayingFile = playlists[currentPlaylistIndex]
          .songs[playlists[currentPlaylistIndex].currentIndex];
    }
    _savePlaybackState(); // Save playback state
    setState(() {});
  }

  Future<void> _browseInitialDirectory() async {
    String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
    if (selectedDirectory != null) {
      _navigateToDirectory(selectedDirectory);
    }
  }

  Future<void> _navigateToDirectory(String? newPath) async {
    debugPrint('Navigating to directory: $newPath');
    if (newPath == null) {
      setState(() {
        _currentBrowsingPath = null;
        _currentBrowseContents = [];
      });
      return;
    }

    // Skip restricted directories
    if (_isRestrictedDirectory(newPath)) {
      debugPrint('Skipping restricted directory: $newPath');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Cannot access restricted directory')),
        );
      }
      return;
    }

    final dir = Directory(newPath);
    if (await dir.exists()) {
      try {
        final contents = await dir.list(followLinks: false).toList();

        // Filter out restricted subdirectories and only show accessible ones
        final filteredContents = contents
            .where((entity) => !_isRestrictedDirectory(entity.path))
            .toList();

        filteredContents.sort((a, b) {
          if (a is Directory && b is! Directory) return -1;
          if (a is! Directory && b is Directory) return 1;
          return path
              .basename(a.path)
              .toLowerCase()
              .compareTo(path.basename(b.path).toLowerCase());
        });
        setState(() {
          _currentBrowsingPath = newPath;
          _currentBrowseContents = filteredContents;
        });
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error browsing directory: Permission denied'),
            ),
          );
        }
        debugPrint('Error browsing directory $newPath: $e');
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Directory does not exist.')),
        );
      }
      debugPrint('Directory does not exist: $newPath');
    }
  }

  String _globToRegex(String glob) {
    String escaped = RegExp.escape(glob);
    escaped = escaped.replaceAll('\\*', '.*');
    escaped = escaped.replaceAll('\\?', '.');
    return escaped;
  }

  void _onSearchChanged() {
    final query = searchController.text.trim();
    debugPrint('Search query changed: "$query"');
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _searchFolderResults.clear();
        _searchFolderPaths.clear();
        _isSearching = false;
      });
    } else {
      setState(() {
        _isSearching = true;
        _searchResults = [];
        _searchFolderResults.clear();
        _searchFolderPaths.clear();
      });
      _searchDeviceForMusic(query);
    }
  }

  // Add this new method to detect pattern-based folders
  bool _isFolderWithPatternedFiles(String folderName, String firstFileName) {
    // Common patterns to check
    final patterns = {
      'call recording': ['call', 'record'],
      'voice recording': ['voice', 'record'],
      'whatsapp audio': ['ptt', 'audio'],
      'camera': ['vid_', 'img_'],
      'downloads': ['download'],
      'recordings': ['record'],
    };

    folderName = folderName.toLowerCase();
    firstFileName = firstFileName.toLowerCase();

    // Check if folder name matches any known pattern
    for (var pattern in patterns.entries) {
      bool matchesPattern = pattern.value.every(
        (word) => folderName.contains(word) || firstFileName.contains(word),
      );
      if (matchesPattern) {
        return true;
      }
    }

    return false;
  }

  // Modified method to check for restricted directories
  bool _isRestrictedDirectory(String directoryPath) {
    final restrictedPatterns = [
      '/Android',
      '/android',
      '/.android',
      '/system',
      '/System',
      '/proc',
      '/dev',
      '/cache',
      '/data/data',
      '/data/system',
      '/data/misc',
      '/config',
      '/acct',
      '/apex',
      '/bin',
      '/bugreports',
      '/d',
      '/debug_ramdisk',
      '/etc',
      '/firmware',
      '/init',
      '/odm',
      '/oem',
      '/product',
      '/sbin',
      '/storage/self',
      '/storage/emulated/0/Android',
      '/vendor',
      'lost.dir',
      '.thumbnails',
      '.tmp',
    ];

    // Check if the path contains any restricted pattern
    for (String pattern in restrictedPatterns) {
      if (directoryPath.contains(pattern)) {
        debugPrint('Skipping restricted directory: $directoryPath');
        return true;
      }
    }

    return false;
  }

  Future<void> _searchDeviceForMusic(String query) async {
    debugPrint('Starting device search for: "$query"');
    final Map<String, List<String>> foundFolders = {};
    final Set<String> processedPaths =
        {}; // To track processed paths and avoid duplicates
    final regexPattern = _globToRegex(query).toLowerCase();
    final regExp = RegExp(regexPattern);
    bool hasShownCompletion = false;

    // Show searching state
    setState(() {
      _isSearching = true;
      _searchResults = [];
      _searchFolderResults.clear();
      _searchFolderPaths.clear();
    });

    try {
      // Get root storage paths
      List<String> rootPaths = [];

      // Add primary storage
      final externalStorage = await getExternalStorageDirectory();
      if (externalStorage != null) {
        final String rootExternal = externalStorage.path
            .split('/Android')
            .first;
        rootPaths.add(rootExternal);
      }

      // Add potential SD card paths
      final sdCardPaths = ['/storage', '/mnt', '/sdcard'];

      // Add all potential storage locations
      for (String basePath in sdCardPaths) {
        try {
          final dir = Directory(basePath);
          if (await dir.exists()) {
            await for (var entity in dir.list()) {
              if (entity is Directory && !_isRestrictedDirectory(entity.path)) {
                rootPaths.add(entity.path);
              }
            }
          }
        } catch (e) {
          debugPrint('Error accessing path $basePath: $e');
        }
      }

      // Remove duplicates and sort
      rootPaths = rootPaths.toSet().toList()..sort();
      debugPrint('Root paths to scan: $rootPaths');

      // Process each root path
      for (String rootPath in rootPaths) {
        try {
          await _scanDirectoryForMusic(
            rootPath,
            regExp,
            foundFolders,
            processedPaths,
            query.toLowerCase(), // Pass the original query for exact matching
          );
        } catch (e) {
          debugPrint('Error scanning root path $rootPath: $e');
        }
      }

      // Update UI with results
      setState(() {
        _searchFolderResults = foundFolders;
        _searchFolderPaths = foundFolders.keys.toList()..sort();
        _isSearching = false;
      });

      // Show completion message only once
      if (mounted && !hasShownCompletion) {
        hasShownCompletion = true;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 10),
                Expanded(
                  child: Text(
                    'Search completed. Found ${foundFolders.length} folders with matching content.',
                    style: TextStyle(color: Colors.white),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error during search: $e');
      setState(() {
        _isSearching = false;
      });

      // Error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.white),
                SizedBox(width: 10),
                Expanded(
                  child: Text(
                    'Search error: ${e.toString()}',
                    style: TextStyle(color: Colors.white),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
            duration: Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _scanDirectoryForMusic(
    String directoryPath,
    RegExp queryRegex,
    Map<String, List<String>> results,
    Set<String> processedPaths,
    String originalQuery,
  ) async {
    // Skip if already processed
    if (processedPaths.contains(directoryPath)) {
      return;
    }

    // Skip restricted directories
    if (_isRestrictedDirectory(directoryPath)) {
      return;
    }

    final dir = Directory(directoryPath);
    if (!await dir.exists()) {
      return;
    }

    processedPaths.add(directoryPath);
    debugPrint('Scanning directory: $directoryPath');

    try {
      final List<FileSystemEntity> contents = await dir.list().toList();
      bool foundMusicFile = false;
      String? firstMusicFileName;
      List<String> musicFiles = [];
      final folderName = path.basename(directoryPath).toLowerCase();

      // Check for exact folder name match first
      bool isDirectFolderMatch = folderName.contains(originalQuery);

      // Scan for files
      for (var entity in contents) {
        if (entity is File) {
          final fileName = path.basename(entity.path).toLowerCase();
          final fileNameWithoutExt = path.basenameWithoutExtension(fileName);

          if (fileName.endsWith('.mp3') ||
              fileName.endsWith('.m4a') ||
              fileName.endsWith('.wav') ||
              fileName.endsWith('.ogg') ||
              fileName.endsWith('.aac') ||
              fileName.endsWith('.flac')) {
            foundMusicFile = true;
            firstMusicFileName = fileName;

            // Check for exact file name match
            bool isExactFileMatch = fileNameWithoutExt.contains(originalQuery);

            if (isDirectFolderMatch || isExactFileMatch) {
              musicFiles.add(entity.path);
            }

            // If this is a direct folder match, continue scanning
            if (isDirectFolderMatch) {
              continue;
            } else if (isExactFileMatch) {
              // If we found an exact file match, add it
              if (!results.containsKey(directoryPath)) {
                results[directoryPath] = [];
              }
              if (!results[directoryPath]!.contains(entity.path)) {
                results[directoryPath]!.add(entity.path);
              }
            }
          }
        }
      }

      // If this is a directly matching folder with music files, add all its music files
      if (isDirectFolderMatch && foundMusicFile) {
        results[directoryPath] = musicFiles;
        debugPrint(
          'Added matching folder: $directoryPath with ${musicFiles.length} files',
        );
        return; // Don't scan subdirectories of matching folders
      }

      // If we found a music file, check for patterns only if we haven't found direct matches
      if (foundMusicFile &&
          firstMusicFileName != null &&
          !isDirectFolderMatch) {
        bool isPatternedFolder = _isFolderWithPatternedFiles(
          folderName,
          firstMusicFileName,
        );

        if (isPatternedFolder &&
            (folderName.contains(originalQuery) ||
                path
                    .basenameWithoutExtension(firstMusicFileName)
                    .contains(originalQuery))) {
          results[directoryPath] = musicFiles;
          debugPrint('Added patterned folder: $directoryPath');
          return;
        }
      }

      // Recursively scan subdirectories
      for (var entity in contents) {
        if (entity is Directory && !_isRestrictedDirectory(entity.path)) {
          await _scanDirectoryForMusic(
            entity.path,
            queryRegex,
            results,
            processedPaths,
            originalQuery,
          );
        }
      }
    } catch (e) {
      debugPrint('Error scanning directory $directoryPath: $e');
    }
  }

  // Format duration to MM:SS format
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String minutes = twoDigits(duration.inMinutes);
    String seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  // Check if a folder is currently playing
  bool _isFolderPlaying(String folderPath) {
    if (_audioService.currentPlaylist == null || !_audioService.isPlaying) {
      return false;
    }

    final currentSong = _audioService
        .currentPlaylist!
        .songs[_audioService.currentPlaylist!.currentIndex];
    return path.dirname(currentSong) == folderPath;
  }

  // Toggle play/pause for a specific folder
  void _toggleFolderPlayback(String folderPath) {
    if (_isFolderPlaying(folderPath)) {
      // If this folder is currently playing, pause it
      _audioService.togglePlayPause();
    } else {
      // If this folder is not playing, start playing it
      _playFolder(folderPath);
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            const SizedBox(height: 20),
            // Triangle layout: SELECT centered, ADD and PLAY spaced below
            Column(
              children: [
                // SELECT button centered
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: Icon(Icons.remove, color: Colors.blue),
                      onPressed: () {
                        if (currentPlaylistIndex > 0) {
                          setState(() {
                            currentPlaylistIndex--;
                          });
                          _savePlaylists();
                        }
                      },
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _showAllSections = false;
                        });
                      },
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.blue.withOpacity(0.2),
                          border: Border.all(color: Colors.blue, width: 2),
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text(
                                'SELECT',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                (currentPlaylistIndex + 1).toString(),
                                style: const TextStyle(
                                  color: Colors.blue,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.add, color: Colors.blue),
                      onPressed: () {
                        if (currentPlaylistIndex < playlists.length - 1) {
                          setState(() {
                            currentPlaylistIndex++;
                          });
                          _savePlaylists();
                        }
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                // ADD and PLAY row
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _showAllSections = !_showAllSections;
                          });
                        },
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _showAllSections
                                ? Colors.grey.withOpacity(0.2)
                                : Colors.grey.withOpacity(0.05),
                            border: Border.all(color: Colors.grey, width: 2),
                          ),
                          child: Center(
                            child: Text(
                              'ADD',
                              style: TextStyle(
                                color: Color(0xFF4388CC),
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        width: 80,
                        height: 80,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Color(0xFFFFCC33),
                        ),
                        child: GestureDetector(
                          onTap: () {
                            if (_audioService.currentPlaylist == null) {
                              _playPlaylist();
                            } else {
                              _audioService.togglePlayPause();
                              setState(() {});
                            }
                          },
                          child: Center(
                            child: Text(
                              _audioService.isPlaying ? 'PAUSE' : 'PLAY',
                              style: TextStyle(
                                color: const Color(0xFF0A612E),
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Container(height: 2, color: Colors.blue),
            const SizedBox(height: 20),
            // Scrollable content
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  if (_showAllSections) ...[
                    // 1. SEARCH SECTION
                    const Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 20.0,
                        vertical: 8.0,
                      ),
                      child: Text(
                        '1. SEARCH',
                        style: TextStyle(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: searchController,
                              style: const TextStyle(
                                color: Color(0xFF4388CC),
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                              decoration: InputDecoration(
                                hintText: 'search MP3 by name folder or file',
                                hintStyle: const TextStyle(
                                  color: Color(0xFF4388CC),
                                  fontWeight: FontWeight.bold,
                                ),
                                suffixIcon: const Icon(
                                  Icons.search,
                                  color: Colors.red,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10.0),
                                  borderSide: BorderSide.none,
                                ),
                                filled: true,
                                fillColor: const Color(0xFFFFCC33),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Search Results List
                    if (_isSearching)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(32.0),
                          child: Column(
                            children: [
                              CircularProgressIndicator(
                                color: Color(0xFF4388CC),
                                strokeWidth: 3.0,
                              ),
                              SizedBox(height: 16.0),
                              Text(
                                'Searching device and SD card...',
                                style: TextStyle(
                                  color: Color(0xFF4388CC),
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    if (!_isSearching && searchController.text.isNotEmpty)
                      _searchFolderPaths.isEmpty
                          ? const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Text(
                                'No results found.',
                                style: TextStyle(color: Color(0xFF4388CC)),
                              ),
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              itemCount: _searchFolderPaths.length,
                              itemBuilder: (context, index) {
                                final folderPath = _searchFolderPaths[index];
                                final folderName = path.basename(folderPath);
                                final musicFiles =
                                    _searchFolderResults[folderPath] ?? [];
                                final isExpanded =
                                    _expandedFolders[folderPath] ?? false;

                                return Container(
                                  margin: EdgeInsets.symmetric(
                                    vertical: 2.0,
                                    horizontal: 0.0,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Color(0xFFFFCC33),
                                    borderRadius: BorderRadius.circular(8.0),
                                    border: Border.all(
                                      color: Color(0xFFFFCC33),
                                    ),
                                  ),
                                  child: ExpansionTile(
                                    leading: Icon(
                                      Icons.folder,
                                      color: Color(0xFFFFCC33),
                                    ),
                                    title: Text(
                                      folderName,
                                      style: TextStyle(
                                        color: Colors.black87,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${musicFiles.length} music files',
                                          style: TextStyle(
                                            color: Colors.black54,
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          folderPath,
                                          style: TextStyle(
                                            color: Colors.black45,
                                            fontSize: 10,
                                            fontFamily: 'monospace',
                                          ),
                                        ),
                                      ],
                                    ),
                                    trailing: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        TextButton(
                                          child: Text(
                                            'ADD',
                                            style: TextStyle(
                                              color: Color(0xFF4388CC),
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          onPressed: () =>
                                              _addFolderToPlaylist(folderPath),
                                        ),
                                      ],
                                    ),
                                    children: musicFiles.map((filePath) {
                                      final fileName = path.basename(filePath);
                                      return ListTile(
                                        leading: Icon(
                                          Icons.music_note,
                                          color: Color(0xFF4388CC),
                                        ),
                                        title: Text(
                                          fileName,
                                          style: TextStyle(
                                            color: Color(0xFF4388CC),
                                          ),
                                        ),
                                        trailing: TextButton(
                                          child: Text(
                                            'ADD',
                                            style: TextStyle(
                                              color: Color(0xFF4388CC),
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          onPressed: () =>
                                              _addFileToPlaylist(filePath),
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                );
                              },
                            ),
                    const SizedBox(height: 20),
                    // 2. BROWSE SECTION
                    const Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 20.0,
                        vertical: 8.0,
                      ),
                      child: Text(
                        '2. FOLDER',
                        style: TextStyle(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    const Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 20.0,
                        vertical: 0.0,
                      ),
                      child: Text(
                        'BROWSE',
                        style: TextStyle(
                          color: Color(0xFF4388CC),
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    _buildFileBrowserContent(_currentBrowseContents),
                    // 3. PLAYLIST SECTION
                    _buildPlaylistSection(),
                  ] else ...[
                    // Only show playlist number and list
                    const Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 20.0,
                        vertical: 8.0,
                      ),
                      child: Text(
                        '3. PLAYLIST',
                        style: TextStyle(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    _buildPlaylistSection(),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileBrowserContent(
    List<FileSystemEntity> contents, {
    int indentLevel = 0,
  }) {
    // Filter contents to show only music files and directories that contain music
    List<FileSystemEntity> filteredContents = contents.where((entity) {
      if (entity is Directory) {
        // Skip restricted directories
        if (_isRestrictedDirectory(entity.path)) {
          return false;
        }
        return true; // We'll check for music files inside when expanding
      } else {
        // Only show music files
        final fileName = entity.path.toLowerCase();
        return fileName.endsWith('.mp3') ||
            fileName.endsWith('.wav') ||
            fileName.endsWith('.flac') ||
            fileName.endsWith('.m4a') ||
            fileName.endsWith('.aac') ||
            fileName.endsWith('.ogg');
      }
    }).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: filteredContents.map((entity) {
        final isDirectory = entity is Directory;
        final name = path.basename(entity.path);
        final padding = EdgeInsets.only(left: 4.0 * indentLevel, right: 4.0);
        if (isDirectory) {
          final dir = Directory(entity.path);
          return FutureBuilder<List<FileSystemEntity>>(
            future: dir.list().toList(),
            builder: (context, snapshot) {
              final children = snapshot.data ?? [];
              return Padding(
                padding: padding,
                child: ExpansionTile(
                  leading: Icon(Icons.folder, color: Color(0xFFFFCC33)),
                  title: Text(name, style: TextStyle(color: Color(0xFF4388CC))),
                  trailing: TextButton(
                    child: const Text(
                      'ADD',
                      style: TextStyle(
                        color: Color(0xFF4388CC),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    onPressed: () => _addFolderToPlaylist(entity.path),
                  ),
                  children: [
                    if (children.isNotEmpty)
                      _buildFileBrowserContent(
                        children,
                        indentLevel: indentLevel + 1,
                      ),
                  ],
                ),
              );
            },
          );
        } else {
          return Padding(
            padding: padding,
            child: ListTile(
              leading: Icon(Icons.music_note, color: Color(0xFF4388CC)),
              title: Text(name, style: TextStyle(color: Color(0xFF4388CC))),
              trailing: TextButton(
                child: const Text(
                  'ADD',
                  style: TextStyle(
                    color: Color(0xFF4388CC),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                onPressed: () => _addFileToPlaylist(entity.path),
              ),
            ),
          );
        }
      }).toList(),
    );
  }

  Widget _buildPlaylistSection() {
    // Safety check to prevent RangeError
    if (playlists.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
            child: Text(
              '3. PLAYLIST (Loading...)',
              style: TextStyle(
                color: Colors.blue,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.all(20.0),
            child: Text(
              'Loading playlists...',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
          child: Text(
            '3. PLAYLIST ${currentPlaylistIndex + 1}',
            style: TextStyle(
              color: Colors.blue,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),
        SizedBox(height: 300, child: _buildPlaylistContent()),
      ],
    );
  }

  // Build playlist content with folder structure
  Widget _buildPlaylistContent() {
    final currentPlaylist = playlists[currentPlaylistIndex];
    if (currentPlaylist.songs.isEmpty) {
      return Center(
        child: Text(
          'Playlist is empty',
          style: TextStyle(color: Colors.grey[600]),
        ),
      );
    }

    // Group songs by folder
    Map<String, List<String>> folderGroups = {};
    for (String songPath in currentPlaylist.songs) {
      String folderPath = path.dirname(songPath);
      if (!folderGroups.containsKey(folderPath)) {
        folderGroups[folderPath] = [];
      }
      folderGroups[folderPath]!.add(songPath);
    }

    // Sort folders alphabetically
    List<String> sortedFolders = folderGroups.keys.toList()..sort();

    List<Widget> playlistItems = [];
    int itemIndex = 0;

    for (String folderPath in sortedFolders) {
      String folderName = path.basename(folderPath);
      List<String> songsInFolder = folderGroups[folderPath]!;

      // Sort songs in folder alphabetically
      songsInFolder.sort(
        (a, b) => path.basename(a).compareTo(path.basename(b)),
      );

      bool isExpanded = _expandedFolders[folderPath] ?? false;

      // Add folder header
      playlistItems.add(
        Container(
          margin: EdgeInsets.symmetric(vertical: 2.0, horizontal: 0.0),
          decoration: BoxDecoration(
            color: Color(0xFFFFCC33),
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(color: Color(0xFFFFCC33)),
          ),
          child: ListTile(
            leading: Text(
              '${itemIndex + 1}.',
              style: TextStyle(
                color: Colors.black87,
                fontWeight: FontWeight.bold,
              ),
            ),
            title: Row(
              children: [
                Icon(
                  isExpanded ? Icons.folder_open : Icons.folder,
                  color: Colors.black87,
                  size: 24,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    folderName,
                    style: TextStyle(
                      color: Colors.black87,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: Icon(Icons.remove, color: Colors.red[600]),
                  onPressed: () => _removeFolderFromPlaylist(folderPath),
                ),
                IconButton(
                  icon: Icon(
                    _isFolderPlaying(folderPath)
                        ? Icons.pause
                        : Icons.play_arrow,
                    color: Colors.black87,
                  ),
                  onPressed: () => _toggleFolderPlayback(folderPath),
                ),
              ],
            ),
            onTap: () {
              setState(() {
                _expandedFolders[folderPath] = !isExpanded;
              });
            },
          ),
        ),
      );

      // Add songs in folder if expanded
      if (isExpanded) {
        for (int i = 0; i < songsInFolder.length; i++) {
          String songPath = songsInFolder[i];
          String songName = path.basename(songPath);
          int globalIndex = currentPlaylist.songs.indexOf(songPath);
          bool isCurrentlyPlaying =
              _audioService.currentPlaylist != null &&
              _audioService.currentPlaylist!.songs.isNotEmpty &&
              _audioService.currentPlaylist!.currentIndex <
                  _audioService.currentPlaylist!.songs.length &&
              songPath ==
                  _audioService.currentPlaylist!.songs[_audioService
                      .currentPlaylist!
                      .currentIndex] &&
              _audioService.isPlaying;

          playlistItems.add(
            Container(
              margin: EdgeInsets.only(left: 0.0, right: 0.0, bottom: 1.0),
              decoration: BoxDecoration(
                color: isCurrentlyPlaying
                    ? Color(0xFF4388CC)
                    : null, // Blue background for currently playing
                borderRadius: BorderRadius.circular(4.0),
              ),
              child: Column(
                children: [
                  ListTile(
                    leading: Text(
                      '${itemIndex + i + 2}.',
                      style: TextStyle(
                        color: isCurrentlyPlaying
                            ? Colors.white
                            : Colors.black87,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                    title: Row(
                      children: [
                        Icon(
                          Icons.music_note,
                          color: isCurrentlyPlaying
                              ? Colors.white
                              : Color(0xFFFFCC33), // Yellow/gold for files
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                songName,
                                style: TextStyle(
                                  color: isCurrentlyPlaying
                                      ? Colors.white
                                      : Color(
                                          0xFFFFCC33,
                                        ), // Yellow/gold for file names
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                              ),
                              Text(
                                folderName,
                                style: TextStyle(
                                  color: isCurrentlyPlaying
                                      ? Colors.white70
                                      : Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: Icon(Icons.remove, color: Colors.red[600]),
                          onPressed: () {
                            setState(() {
                              playlists[currentPlaylistIndex].removeSong(
                                globalIndex,
                              );
                            });
                            _savePlaylists();
                          },
                        ),
                        IconButton(
                          icon: Icon(
                            isCurrentlyPlaying ? Icons.pause : Icons.play_arrow,
                            color: isCurrentlyPlaying
                                ? Colors.white
                                : Colors.blue[700],
                          ),
                          onPressed: () {
                            if (isCurrentlyPlaying) {
                              _audioService.togglePlayPause();
                            } else {
                              playlists[currentPlaylistIndex].currentIndex =
                                  globalIndex;
                              _audioService.playPlaylist(
                                playlists[currentPlaylistIndex],
                              );
                              _currentlyPlayingFile = songPath;
                              _savePlaybackState();
                            }
                            setState(() {});
                          },
                        ),
                      ],
                    ),
                    dense: true,
                  ),
                  // Add progress bar under each song
                  if (isCurrentlyPlaying)
                    FutureBuilder<Duration?>(
                      future: _audioService.getCurrentPosition(),
                      builder: (context, positionSnapshot) {
                        return FutureBuilder<Duration?>(
                          future: _audioService.getDuration(),
                          builder: (context, durationSnapshot) {
                            double progress = 0.0;
                            String timeText = "0:00 / 0:00";

                            if (positionSnapshot.hasData &&
                                durationSnapshot.hasData &&
                                durationSnapshot.data != null &&
                                durationSnapshot.data!.inMilliseconds > 0) {
                              progress =
                                  positionSnapshot.data!.inMilliseconds /
                                  durationSnapshot.data!.inMilliseconds;
                              progress = progress.clamp(0.0, 1.0);

                              String currentTime = _formatDuration(
                                positionSnapshot.data!,
                              );
                              String totalTime = _formatDuration(
                                durationSnapshot.data!,
                              );
                              timeText = "$currentTime / $totalTime";
                            }

                            return Container(
                              margin: EdgeInsets.symmetric(
                                horizontal: 16.0,
                                vertical: 4.0,
                              ),
                              child: Column(
                                children: [
                                  Slider(
                                    value: progress,
                                    onChanged: (newValue) {
                                      if (durationSnapshot.data != null) {
                                        final newPosition = Duration(
                                          milliseconds:
                                              (newValue *
                                                      durationSnapshot
                                                          .data!
                                                          .inMilliseconds)
                                                  .round(),
                                        );
                                        _audioService.seek(newPosition);
                                      }
                                    },
                                    activeColor: Colors.blue,
                                    inactiveColor: Colors.grey[300],
                                    min: 0.0,
                                    max: 1.0,
                                  ),
                                  SizedBox(height: 2.0),
                                  Text(
                                    timeText,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                ],
              ),
            ),
          );
        }
      }

      itemIndex += songsInFolder.length;
    }

    return ListView(children: playlistItems);
  }

  @override
  void dispose() {
    _progressTimer?.cancel();
    _audioService.dispose();
    searchController.removeListener(_onSearchChanged);
    searchController.dispose();
    super.dispose();
  }
}
