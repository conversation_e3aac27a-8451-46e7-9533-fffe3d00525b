import 'dart:io';

import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:mp3_player/screens/player_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MP3 Player',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Roboto',
        scaffoldBackgroundColor: const Color(0xFF1E1E1E),
        textTheme: const TextTheme(
          bodyLarge: TextStyle(color: Colors.white),
          bodyMedium: TextStyle(color: Colors.white),
        ),
      ),
      home: const PlayerScreen(),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _selectedPlaylist = 1;
  int _addPlaylist = 1;

  final AudioPlayer _audioPlayer = AudioPlayer();
  List<List<String>> _playlists = [[], [], []];
  int _currentSongIndex = -1;
  List<String> _searchResults = [];
  bool _isSearching = false;

  void _incrementSelectedPlaylist() {
    setState(() {
      if (_selectedPlaylist < 3) {
        _selectedPlaylist++;
      }
    });
  }

  void _decrementSelectedPlaylist() {
    setState(() {
      if (_selectedPlaylist > 1) {
        _selectedPlaylist--;
      }
    });
  }

  void _incrementAddPlaylist() {
    setState(() {
      if (_addPlaylist < 3) {
        _addPlaylist++;
      }
    });
  }

  void _decrementAddPlaylist() {
    setState(() {
      if (_addPlaylist > 1) {
        _addPlaylist--;
      }
    });
  }

  Future<void> _addFiles() async {
    if (await Permission.storage.request().isGranted) {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.custom,
        allowedExtensions: ['mp3'],
      );

      if (result != null) {
        setState(() {
          _playlists[_addPlaylist - 1].addAll(
            result.paths.map((path) => path!),
          );
        });
      }
    }
  }

  Future<void> _addFolder() async {
    var status = await Permission.storage.status;
    if (!status.isGranted) {
      status = await Permission.storage.request();
    }
    if (status.isGranted) {
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();

      if (selectedDirectory != null) {
        final dir = Directory(selectedDirectory);
        final List<FileSystemEntity> entities = await dir.list().toList();
        final List<String> mp3s = [];
        for (var entity in entities) {
          if (entity.path.toLowerCase().endsWith('.mp3')) {
            print('Found mp3 file: \'${entity.path}\'');
            mp3s.add(entity.path);
          }
        }
        setState(() {
          _playlists[_addPlaylist - 1].addAll(mp3s);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            const SizedBox(height: 50),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: const Icon(Icons.remove),
                  onPressed: _decrementSelectedPlaylist,
                ),
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.blue, width: 2),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text(
                          'SELECT',
                          style: TextStyle(color: Colors.blue, fontSize: 16),
                        ),
                        Text(
                          '$_selectedPlaylist',
                          style: const TextStyle(
                            color: Colors.blue,
                            fontSize: 24,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: _incrementSelectedPlaylist,
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.remove),
                      onPressed: _decrementAddPlaylist,
                    ),
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey[300],
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Text(
                              'ADD',
                              style: TextStyle(
                                color: Colors.blue,
                                fontSize: 14,
                              ),
                            ),
                            Text(
                              '$_addPlaylist',
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: 20,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.add),
                      onPressed: _incrementAddPlaylist,
                    ),
                  ],
                ),
                GestureDetector(
                  onTap: _playPlaylist,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.yellow,
                    ),
                    child: const Center(
                      child: Text(
                        'PLAY',
                        style: TextStyle(color: Colors.black, fontSize: 16),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            const Divider(color: Colors.blue, thickness: 2),
            const SizedBox(height: 20),
            Row(
              children: [
                const Text('1.', style: TextStyle(fontSize: 16)),
                const SizedBox(width: 10),
                Expanded(
                  child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.yellow,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: TextField(
                      decoration: const InputDecoration(
                        hintText: 'Search for MP3s...',
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(horizontal: 20),
                      ),
                      onSubmitted: _searchFiles,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                const Text(
                  '2. FOLDER',
                  style: TextStyle(color: Colors.blue, fontSize: 16),
                ),
                const SizedBox(width: 20),
                GestureDetector(
                  onTap: _addFolder,
                  child: const Text(
                    'BROWSE',
                    style: TextStyle(color: Colors.yellow, fontSize: 16),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Expanded(child: _buildPlaylistView()),
          ],
        ),
      ),
    );
  }

  void _playPlaylist() {
    if (_playlists[_selectedPlaylist - 1].isNotEmpty) {
      _currentSongIndex = 0;
      _playSong(_playlists[_selectedPlaylist - 1][_currentSongIndex]);
    }
  }

  void _playSong(String path) {
    _audioPlayer.play(DeviceFileSource(path));
    _audioPlayer.onPlayerComplete.listen((event) {
      _playNext();
    });
  }

  void _playNext() {
    if (_currentSongIndex < _playlists[_selectedPlaylist - 1].length - 1) {
      _currentSongIndex++;
      _playSong(_playlists[_selectedPlaylist - 1][_currentSongIndex]);
    } else {
      _currentSongIndex = -1;
    }
  }

  Future<void> _searchFiles(String query) async {
    var status = await Permission.storage.status;
    if (!status.isGranted) {
      status = await Permission.storage.request();
    }
    if (status.isGranted) {
      setState(() {
        _isSearching = true;
        _searchResults.clear();
      });

      final Directory? externalDir = await getExternalStorageDirectory();
      if (externalDir != null) {
        final Stream<FileSystemEntity> entities = externalDir.list(
          recursive: true,
          followLinks: false,
        );
        await for (FileSystemEntity entity in entities) {
          if (entity.path.toLowerCase().contains(query.toLowerCase())) {
            setState(() {
              _searchResults.add(entity.path);
            });
          }
        }
      }

      setState(() {
        _isSearching = false;
      });
    }
  }

  Widget _buildPlaylistView() {
    if (_isSearching) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_searchResults.isNotEmpty) {
      return _buildSearchResults();
    }

    final playlist = _playlists[_selectedPlaylist - 1];
    if (playlist.isEmpty) {
      return const Center(child: Text('Playlist is empty'));
    }
    return ListView.builder(
      itemCount: playlist.length,
      itemBuilder: (context, index) {
        final path = playlist[index];
        final fileName = path.split('/').last;
        return ListTile(
          title: Text(fileName),
          leading: _currentSongIndex == index
              ? const Icon(Icons.play_arrow, color: Colors.blue)
              : const Icon(Icons.music_note),
          onTap: () {
            _currentSongIndex = index;
            _playSong(path);
          },
        );
      },
    );
  }

  Widget _buildSearchResults() {
    return ListView.builder(
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final path = _searchResults[index];
        final fileName = path.split('/').last;
        return ListTile(
          title: Text(fileName),
          leading: const Icon(Icons.music_note),
          onTap: () {
            // Add the searched song to the selected playlist
            setState(() {
              _playlists[_addPlaylist - 1].add(path);
            });
          },
        );
      },
    );
  }
}
