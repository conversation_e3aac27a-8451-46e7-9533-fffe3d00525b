import 'dart:io';
import 'package:path/path.dart' as path;

class Playlist {
  final String name;
  final List<String> songs;
  int currentIndex;

  Playlist({required this.name, List<String>? songs, this.currentIndex = 0})
    : songs = songs ?? [];

  void addSong(String songPath) {
    songs.add(songPath);
  }

  void addSongs(List<String> songPaths) {
    songs.addAll(songPaths);
  }

  void removeSong(int index) {
    if (index >= 0 && index < songs.length) {
      songs.removeAt(index);
      if (currentIndex >= songs.length) {
        currentIndex = songs.isEmpty ? 0 : songs.length - 1;
      }
    }
  }

  String? getCurrentSong() {
    if (songs.isEmpty) return null;
    return songs[currentIndex];
  }

  String getCurrentSongName() {
    if (songs.isEmpty) return '';
    return path.basename(songs[currentIndex]);
  }

  bool hasNextSong() {
    return songs.isNotEmpty && currentIndex < songs.length - 1;
  }

  void nextSong() {
    if (songs.isNotEmpty && currentIndex < songs.length - 1) {
      currentIndex++;
    }
  }

  bool hasPreviousSong() {
    return songs.isNotEmpty && currentIndex > 0;
  }

  void previousSong() {
    if (songs.isNotEmpty && currentIndex > 0) {
      currentIndex--;
    }
  }
}
